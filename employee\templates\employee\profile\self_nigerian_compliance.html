{% extends 'nigerian_compliance/compliance_form_as_p.html' %}{% load i18n %}
{% block content %}
<div class="container">
    {% if messages %}
    <div class="oh-alert-container">
        {% for message in messages %}
        <div class="oh-alert oh-alert--animated {{message.tags}}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">
                <i class="fas fa-shield-alt me-2"></i>
                {% trans "Update Your Nigerian Compliance Information" %}
            </h4>
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'self-nigerian-compliance-update' %}">
                {% csrf_token %}
                {{form.as_p}}
                
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>{% trans "Update My Compliance Information" %}
                    </button>
                    <a href="{% url 'employee-profile' %}" class="btn btn-secondary ms-2">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Profile" %}
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}